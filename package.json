{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 4000", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install"}, "lint-staged": {"./src/**/*.{ts,js,jsx,tsx}": ["prettier --write", "eslint"]}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vladmandic/face-api": "^1.7.15", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "eslint": "^8", "eslint-config-next": "14.0.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "geist": "^1.3.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next-themes": "^0.4.6", "react-day-picker": "9.8.0", "react-hook-form": "^7.60.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.4", "sonner": "^1.7.4", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "jotai": "^2.13.1", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind": "^4.0.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.9", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "husky": "^9.1.7", "postcss": "^8.5", "tailwindcss": "^4.1.9", "tw-animate-css": "1.3.3", "typescript": "^5"}, "overrides": {"body-parser": "1.20.3", "jsonwebtoken": "9.0.0", "lodash": "4.17.21", "moment": "2.29.4", "path-to-regexp": "0.1.12", "qs": "6.11.0", "ws": "6.2.3"}}