{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "next/core-web-vitals"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-non-null-asserted-optional-chain": "off", "indent": ["off", 2, {"switchCase": 4}], "linebreak-style": ["off", "unix"], "no-console": "error", "no-unused-vars": "error", "array-callback-return": "error", "semi": ["off", "never"], "react/no-unescaped-entities": "off", "react-hooks/exhaustive-deps": "off", "react/no-unknown-property": "off", "no-extra-boolean-cast": "off"}}